<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\AdminController;

Route::get('/', function () {
    return view('welcome');
});

Auth::routes();

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

// Admin routes - protected by auth middleware
Route::middleware(['auth'])->group(function () {
    Route::get('/admin/{page}', [AdminController::class, 'index'])->name('admin.page');
});

// Public pages that don't require authentication
Route::get('/signin', function () {
    return view('signin');
});

Route::get('/signup', function () {
    return view('signup');
});

Route::get('/forgot', function () {
    return view('forgot');
});

Route::get('/reset', function () {
    return view('reset');
});

Route::get('/underconstruction', function () {
    return view('underconstruction');
});

Route::get('/icons', function () {
    return view('icons');
});

Route::get('/404', function () {
    return view('404');
});

// Fallback route for other pages (should be last) - only for authenticated users
Route::middleware(['auth'])->group(function () {
    Route::get('/{page}', [AdminController::class, 'index'])->where('page', '^(?!login|register|password|home|admin|signin|signup|forgot|reset|underconstruction|icons|404).*$');
});
