<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\AdminController;

Route::get('/', function () {
    return view('welcome');
});

Auth::routes();

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

// Admin routes - protected by auth middleware
Route::middleware(['auth'])->group(function () {
    Route::get('/admin/{page}', [AdminController::class, 'index'])->name('admin.page');
});

// Fallback route for other pages (should be last)
Route::get('/{page}', [AdminController::class, 'index'])->where('page', '^(?!login|register|password|home|admin).*$');
